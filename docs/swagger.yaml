basePath: /
definitions:
  domain.AuthInputLogin:
    properties:
      email:
        type: string
      password:
        type: string
    required:
    - email
    - password
    type: object
  domain.Cuti:
    properties:
      hrm_employee_id:
        type: string
      outlet_fkid:
        type: string
      trc_created:
        type: string
      trc_date_end:
        type: string
      trc_date_start:
        type: string
      trc_id:
        type: string
      trc_reason:
        type: string
      trc_status:
        type: string
      trc_update:
        type: string
      type:
        type: string
    type: object
  domain.CutiV2Detail:
    properties:
      action:
        type: integer
      address:
        type: string
      created:
        type: string
      descr:
        type: string
      employee_id:
        type: integer
      employee_name:
        type: string
      end_date:
        type: string
      nik:
        type: string
      "no":
        type: integer
      outlet:
        type: string
      outlet_fkid:
        type: integer
      phone:
        type: string
      reason:
        type: string
      start_date:
        type: string
      status:
        type: string
      trc_id:
        type: integer
    type: object
  domain.Device:
    properties:
      device:
        type: string
      device_info:
        type: string
      id:
        type: integer
      token:
        type: string
      user_id:
        type: string
      user_type:
        type: string
    type: object
  domain.Employee:
    properties:
      EMP_ID:
        type: integer
      HEMP_FKID:
        type: integer
      HEMP_ID:
        type: integer
      ID:
        type: integer
      MAX_LEAVE:
        type: integer
      NAME:
        type: string
      NIK:
        type: string
      "NO":
        type: integer
      SALLARY:
        type: integer
      TYPE_FKID:
        type: integer
      TYPE_NAME:
        type: string
      admin_fkid:
        type: integer
      data_created:
        type: string
      data_modified:
        type: string
      data_status:
        type: string
      date_join:
        type: integer
    type: object
  domain.EmployeeSchedule:
    properties:
      employee_id:
        type: integer
      name:
        type: string
      nik:
        type: string
      outlet:
        type: string
    type: object
  domain.HrmAddInfo:
    properties:
      add_id:
        type: integer
      attachment:
        type: string
      hrm_employee_fkid:
        type: integer
      info:
        type: string
      title:
        type: string
    type: object
  domain.HrmMasterType:
    properties:
      admin_fkid:
        type: integer
      rest_hours:
        type: integer
      type_hours:
        type: integer
      type_id:
        type: integer
      type_name:
        type: string
    type: object
  domain.HrmType:
    properties:
      ACTION:
        type: integer
      "NO":
        type: integer
      REST_HOURS:
        type: integer
      TYPE_HOURS:
        type: integer
      TYPE_NAME:
        type: string
      admin_fkid:
        type: integer
    type: object
  domain.MasterShift:
    properties:
      shift_active:
        type: integer
      shift_code:
        type: string
      shift_color:
        type: string
      shift_fkid:
        type: integer
      shift_id:
        type: integer
      shift_id_name:
        type: integer
      shift_in:
        type: string
      shift_max_in:
        type: string
      shift_name:
        type: string
      shift_name_id:
        type: string
      shift_office:
        type: string
      shift_office_id:
        type: integer
      shift_out:
        type: string
      shift_tolerance:
        type: integer
      shift_type:
        type: string
      shift_type_id:
        type: integer
    type: object
  domain.Message:
    properties:
      admin_fkid:
        type: integer
      data_created:
        type: integer
      is_read:
        type: integer
      is_viewed:
        type: integer
      message:
        type: string
      notification_data:
        type: string
      notification_id:
        type: integer
      notification_type:
        type: string
      receiver_id:
        type: string
      title:
        type: string
      type:
        type: string
    type: object
  domain.Presensi:
    properties:
      conf_late_break:
        type: string
      employee_id:
        type: string
      late_break_in:
        type: string
      overtime:
        type: string
      promise:
        type: string
      tip_confirm_terlambat:
        type: string
      tip_hash:
        type: string
      tip_id:
        type: string
      tip_is_terlambat:
        type: string
      tip_jam:
        type: string
      tip_kode:
        type: string
      tip_name_karyawan:
        type: string
      tip_nik:
        type: string
      tip_outlet_id:
        type: string
      tip_tanggal:
        type: string
      tipe_terlambat:
        type: string
    type: object
  domain.ShiftAdd:
    properties:
      shift_active:
        type: integer
      shift_code:
        type: string
      shift_color:
        type: string
      shift_fkid:
        type: integer
      shift_id:
        type: integer
      shift_in:
        type: string
      shift_office:
        type: string
      shift_out:
        type: string
      shift_tolerance:
        type: integer
      shift_type:
        type: string
    type: object
  domain.ShiftGroup:
    properties:
      id:
        type: integer
      outlet_fkid:
        type: integer
      shift_fkid:
        type: integer
      shift_name:
        type: string
    type: object
  domain.ShiftTime:
    properties:
      rest_hours:
        type: integer
      shift_in:
        type: string
      shift_out:
        type: string
      shift_tolerance:
        type: integer
      shift_type:
        type: integer
      type_hours:
        type: integer
      type_name:
        type: string
    type: object
  domain.Token:
    properties:
      expired:
        type: integer
      token:
        type: string
      type:
        type: string
    type: object
  domain.User:
    properties:
      business_id:
        type: string
      email:
        type: string
      name:
        type: string
      phone:
        type: string
      user_id:
        type: string
      user_type:
        type: string
    type: object
  domain.UserRole:
    properties:
      outletAccess:
        type: string
    type: object
  domain.UserToken:
    properties:
      token:
        $ref: '#/definitions/domain.Token'
      user:
        $ref: '#/definitions/domain.User'
      user_role:
        $ref: '#/definitions/domain.UserRole'
    type: object
  domain.VectorImg:
    properties:
      img_vector:
        type: string
    type: object
  fiber.Map:
    additionalProperties: true
    type: object
host: apis.uniqdev.web.id/hrm
info:
  contact: {}
  description: API Documentation for UNIQ HRM System
  title: UNIQ HRM API
  version: "1.0"
paths:
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate user and return access token
      parameters:
      - description: Login credentials
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/domain.AuthInputLogin'
      produces:
      - application/json
      responses:
        "200":
          description: Successfully authenticated with user details and token
          schema:
            $ref: '#/definitions/domain.UserToken'
        "400":
          description: Bad request - Invalid input
          schema: {}
        "401":
          description: Unauthorized - Invalid email or password
          schema: {}
        "404":
          description: User not found
          schema: {}
        "422":
          description: Unprocessable entity
          schema: {}
      summary: Login to the system
      tags:
      - auth
  /cuti/v2:
    get:
      consumes:
      - application/json
      description: Fetch cuti v2
      parameters:
      - description: Bearer {token}
        in: header
        name: Authorization
        required: true
        type: string
      - description: Comma-separated list of outlet IDs
        in: query
        name: outlet_ids
        type: string
      - description: Comma-separated list of statuses (pending,approved,rejected)
        in: query
        name: status
        type: string
      - description: Start date in DD-MM-YYYY format
        in: query
        name: start_date
        type: string
      - description: End date in DD-MM-YYYY format
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/fiber.Map'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.CutiV2Detail'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fiber.Map'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Fetch cuti v2
      tags:
      - Cuti
  /v0/add_employee:
    post:
      consumes:
      - multipart/form-data
      description: Add a new employee or update an existing one with optional additional
        information and attachments
      parameters:
      - description: Employee Type ID (empty for new employee)
        in: formData
        name: inputTypeId
        type: string
      - description: Employee Name
        in: formData
        name: inputEmmployeeName
        required: true
        type: string
      - description: NIK (16 digits)
        in: formData
        name: inputNik
        required: true
        type: string
      - description: Employee Salary
        in: formData
        name: inputSallary
        required: true
        type: string
      - description: Employee Type
        in: formData
        name: inputEmployeeType
        required: true
        type: string
      - description: Maximum Leave Days
        in: formData
        name: inputCuti
        required: true
        type: string
      - description: Full Name
        in: formData
        name: name
        required: true
        type: string
      - description: Address
        in: formData
        name: address
        required: true
        type: string
      - description: Comma-separated list of outlet IDs that the employee has access
          to
        in: formData
        name: outlet_ids
        type: string
      - description: Phone Number
        in: formData
        name: phone
        required: true
        type: string
      - description: Position/Job Title ID
        in: formData
        name: jabatanId
        required: true
        type: string
      - description: Join Date (YYYY-MM-DD)
        in: formData
        name: joinDate
        required: true
        type: string
      - description: NPWP (Tax ID)
        in: formData
        name: npwp
        type: string
      - collectionFormat: csv
        description: Additional Info Titles
        in: formData
        items:
          type: string
        name: inputTitle[]
        type: array
      - collectionFormat: csv
        description: Additional Info IDs (for updates)
        in: formData
        items:
          type: string
        name: info_id[]
        type: array
      - collectionFormat: csv
        description: Has Attachment Flags (true/false)
        in: formData
        items:
          type: string
        name: withFile[]
        type: array
      - collectionFormat: csv
        description: Additional Info Contents
        in: formData
        items:
          type: string
        name: inputInfo[]
        type: array
      - collectionFormat: csv
        description: Attachments
        in: formData
        items:
          type: file
        name: inputAttachment[]
        type: array
      - description: Employee Photo
        in: formData
        name: photo
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "400":
          description: Validation error
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Add or update an employee (DEPRECATED)
      tags:
      - employee
  /v1/add_cuti:
    post:
      consumes:
      - multipart/form-data
      - multipart/form-data
      description: |-
        Add a new cuti record with details and optional document attachments.
        Create a new cuti request with details including dates, reason, and attachments
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: Outlet ID
        in: formData
        name: inputOutletId
        required: true
        type: string
      - description: Employee ID
        in: formData
        name: inputEmployeeId
        required: true
        type: string
      - description: Type of cuti
        in: formData
        name: typeInput
        required: true
        type: string
      - description: Start date of cuti (YYYY-MM-DD)
        in: formData
        name: inputStartDate
        required: true
        type: string
      - description: End date of cuti (YYYY-MM-DD)
        in: formData
        name: inputEndDate
        required: true
        type: string
      - description: Description/Reason for cuti
        in: formData
        name: inputDesc
        required: true
        type: string
      - description: Document attachments
        in: formData
        name: inputDocument[]
        type: file
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: User type (employee/admin)
        in: formData
        name: user_type
        required: true
        type: string
      - description: User ID
        in: formData
        name: user_id
        required: true
        type: string
      - description: Outlet ID
        in: formData
        name: inputOutletId
        required: true
        type: string
      - description: Employee ID
        in: formData
        name: inputEmployeeId
        required: true
        type: string
      - description: Type of cuti (e.g., leave, sick, etc.)
        in: formData
        name: typeInput
        required: true
        type: string
      - description: Start date of cuti (YYYY-MM-DD)
        in: formData
        name: inputStartDate
        required: true
        type: string
      - description: End date of cuti (YYYY-MM-DD)
        in: formData
        name: inputEndDate
        required: true
        type: string
      - description: Description of cuti
        in: formData
        name: inputDesc
        required: true
        type: string
      - description: Document attachments
        in: formData
        name: inputDocument[]
        type: file
      produces:
      - application/json
      - application/json
      responses:
        "200":
          description: Success response with message and status
          schema:
            $ref: '#/definitions/fiber.Map'
        "400":
          description: Bad request or validation error
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Add a new cuti request
      tags:
      - cuti
      - cuti
  /v1/add_employee:
    post:
      consumes:
      - multipart/form-data
      description: Add a new employee or update an existing one with optional additional
        information and attachments
      parameters:
      - description: Employee Type ID (empty for new employee)
        in: formData
        name: inputTypeId
        type: string
      - description: Employee Name
        in: formData
        name: inputEmmployeeName
        required: true
        type: string
      - description: NIK (16 digits)
        in: formData
        name: inputNik
        required: true
        type: string
      - description: Employee Salary
        in: formData
        name: inputSallary
        required: true
        type: string
      - description: Employee Type
        in: formData
        name: inputEmployeeType
        required: true
        type: string
      - description: Maximum Leave Days
        in: formData
        name: inputCuti
        required: true
        type: string
      - description: Full Name
        in: formData
        name: name
        required: true
        type: string
      - description: Address
        in: formData
        name: address
        required: true
        type: string
      - description: Comma-separated list of outlet IDs that the employee has access
          to
        in: formData
        name: outlet_ids
        type: string
      - description: Phone Number
        in: formData
        name: phone
        required: true
        type: string
      - description: Position/Job Title ID
        in: formData
        name: jabatanId
        required: true
        type: string
      - description: Join Date (YYYY-MM-DD)
        in: formData
        name: joinDate
        required: true
        type: string
      - description: NPWP (Tax ID)
        in: formData
        name: npwp
        type: string
      - collectionFormat: csv
        description: Additional Info Titles
        in: formData
        items:
          type: string
        name: inputTitle[]
        type: array
      - collectionFormat: csv
        description: Additional Info IDs (for updates)
        in: formData
        items:
          type: string
        name: info_id[]
        type: array
      - collectionFormat: csv
        description: Has Attachment Flags (true/false)
        in: formData
        items:
          type: string
        name: withFile[]
        type: array
      - collectionFormat: csv
        description: Additional Info Contents
        in: formData
        items:
          type: string
        name: inputInfo[]
        type: array
      - collectionFormat: csv
        description: Attachments
        in: formData
        items:
          type: file
        name: inputAttachment[]
        type: array
      - description: Employee Photo
        in: formData
        name: photo
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "400":
          description: Validation error
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Add or update an employee
      tags:
      - employee
  /v1/all_type:
    get:
      consumes:
      - application/json
      description: Get all employee types filtered by business ID from the header
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: Business ID
        in: header
        name: business_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of employee types
          schema:
            items:
              $ref: '#/definitions/domain.HrmType'
            type: array
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get all employee types with business ID
      tags:
      - type
  /v1/cancel_cuti:
    post:
      consumes:
      - application/json
      description: Cancel an existing cuti record.
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: Cuti cancellation details
        in: body
        name: request
        required: true
        schema:
          type: object
      - description: Transaction Cuti ID
        in: body
        name: request.trc_id
        required: true
        schema:
          type: integer
      - description: Employee ID
        in: body
        name: request.emp_id
        required: true
        schema:
          type: integer
      - description: Outlet ID
        in: body
        name: request.outlet_id
        required: true
        schema:
          type: integer
      - description: Start date of cuti (YYYY-MM-DD)
        in: body
        name: request.date_start
        required: true
        schema:
          type: string
      - description: End date of cuti (YYYY-MM-DD)
        in: body
        name: request.date_end
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Cancel a cuti record (Web)
      tags:
      - cuti
  /v1/create_header:
    post:
      consumes:
      - application/json
      description: Generate header columns for schedule display
      parameters:
      - description: Date parameters
        in: body
        name: input
        required: true
        schema:
          properties:
            date:
              type: string
            fullDate:
              type: string
            startDate:
              type: string
            year:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Header column data
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
            type: object
      summary: Get schedule header columns
      tags:
      - schedule
  /v1/cuti:
    get:
      consumes:
      - application/json
      description: Get all cuti records based on the business ID from the header.
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/domain.Cuti'
            type: array
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get all cuti records based on the business ID from the header.
      tags:
      - cuti
  /v1/delete_attach:
    post:
      consumes:
      - application/json
      description: Delete an attachment from additional employee information
      parameters:
      - description: Attachment ID
        in: body
        name: request
        required: true
        schema:
          type: object
      - description: Attachment ID
        in: body
        name: request.id
        required: true
        schema:
          type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Delete an attachment
      tags:
      - employee
  /v1/delete_cuti:
    post:
      consumes:
      - application/json
      description: Delete an existing cuti record and associated files.
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: Cuti deletion details
        in: body
        name: request
        required: true
        schema:
          type: object
      - description: Transaction Cuti ID
        in: body
        name: request.trc_id
        required: true
        schema:
          type: integer
      - description: Outlet ID
        in: body
        name: request.outlet_id
        required: true
        schema:
          type: integer
      - description: Employee ID
        in: body
        name: request.emp_id
        required: true
        schema:
          type: integer
      - description: Start date of cuti (YYYY-MM-DD)
        in: body
        name: request.start_date
        required: true
        schema:
          type: string
      - description: End date of cuti (YYYY-MM-DD)
        in: body
        name: request.end_date
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Delete a cuti record (Web)
      tags:
      - cuti
  /v1/details_cuti/{id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific cuti record, including
        attachments and records.
      parameters:
      - description: Cuti ID
        in: path
        name: id
        required: true
        type: integer
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Cuti details, attachments, and records
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get cuti details by ID
      tags:
      - cuti
  /v1/employee/{id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific employee including additional
        info. Supports fetching by hrm_employee_id or employee_id. When hrm_employee_id
        is 0 or not available, use employee_id query parameter.
      parameters:
      - description: HRM Employee ID (use 0 when fetching by employee_id)
        in: path
        name: id
        required: true
        type: integer
      - description: Employee ID (required when path id is 0)
        in: query
        name: employee_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Employee details with additional info array
          schema:
            type: object
        "400":
          description: Bad request - missing required parameters
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get a single employee by ID
      tags:
      - employee
  /v1/employee_schedule:
    get:
      consumes:
      - application/json
      description: Get schedule details for a specific employee in an outlet
      parameters:
      - description: Query params
        in: body
        name: input
        required: true
        schema:
          properties:
            employee_id:
              type: integer
            outlet_id:
              type: integer
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Employee schedule details
          schema:
            items:
              $ref: '#/definitions/domain.EmployeeSchedule'
            type: array
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
            type: object
      summary: Get specific employee schedule
      tags:
      - schedule
  /v1/employee_shift:
    get:
      consumes:
      - application/json
      description: Get shift details for employees in an outlet
      parameters:
      - description: Query params
        in: body
        name: input
        required: true
        schema:
          properties:
            outlet_id:
              type: integer
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Shift details and colors
          schema:
            items: {}
            type: array
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
            type: object
      summary: Get employee shifts
      tags:
      - schedule
  /v1/employees:
    get:
      consumes:
      - application/json
      description: Get a list of all employees filtered by business ID from the header
      parameters:
      - description: Business ID
        in: header
        name: business_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of employees
          schema:
            items:
              $ref: '#/definitions/domain.Employee'
            type: array
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get all employees
      tags:
      - employee
  /v1/get_info/{id}:
    get:
      consumes:
      - application/json
      description: Get all additional information records for a specific employee
      parameters:
      - description: Employee ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Additional information records
          schema:
            items:
              $ref: '#/definitions/domain.HrmAddInfo'
            type: array
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get additional information for an employee
      tags:
      - employee
  /v1/presensi:
    get:
      consumes:
      - application/json
      description: Retrieve all employee attendance records including late status,
        overtime, and break times
      produces:
      - application/json
      responses:
        "200":
          description: List of attendance records
          schema:
            items:
              $ref: '#/definitions/domain.Presensi'
            type: array
        "500":
          description: Internal server error
          schema: {}
      summary: Get all attendance records
      tags:
      - presensi
  /v1/presensi/import:
    post:
      consumes:
      - multipart/form-data
      description: Import presensi data from Excel file with outlet ID and overwrite
        option
      parameters:
      - description: Outlet ID
        in: formData
        name: outlet_id
        required: true
        type: string
      - description: Overwrite existing data (true/false)
        in: formData
        name: overwrite
        type: string
      - description: Excel file with presensi data
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            properties:
              message:
                type: string
              status:
                type: integer
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              message:
                type: string
              status:
                type: integer
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
              status:
                type: integer
            type: object
      security:
      - BearerAuth: []
      summary: Import presensi from Excel file
      tags:
      - presensi
  /v1/save_data_schedule:
    post:
      consumes:
      - application/json
      description: Save or update employee shift schedules
      parameters:
      - description: Schedule data
        in: body
        name: input
        required: true
        schema:
          properties:
            data:
              items:
                properties:
                  bulan:
                    type: string
                  employee_id:
                    type: string
                  employee_name:
                    type: string
                  outlet:
                    type: string
                  outlet_id:
                    type: string
                  type:
                    type: string
                  type_id:
                    type: string
                type: object
              type: array
            date:
              type: string
            month:
              type: string
            outlet_id:
              type: string
            year:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            properties:
              message:
                type: string
              status:
                type: integer
            type: object
        "500":
          description: Error response
          schema:
            properties:
              message:
                type: string
              status:
                type: integer
            type: object
      summary: Save employee schedules
      tags:
      - schedule
  /v1/save_details_cuti:
    post:
      consumes:
      - multipart/form-data
      description: Save or update details of a cuti record, including status, reason,
        and attachments.
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: Input Type Admin
        in: formData
        name: inputTypeAdm
        required: true
        type: string
      - description: Input User ID
        in: formData
        name: inputUserID
        required: true
        type: string
      - description: Input Admin ID
        in: formData
        name: inputAdmin
        required: true
        type: string
      - description: Input Admin Email
        in: formData
        name: inputEmailAdm
        required: true
        type: string
      - description: NIK
        in: formData
        name: nik
        required: true
        type: string
      - description: Transaction Cuti ID
        in: formData
        name: inputTransCutiId
        required: true
        type: string
      - description: Employee ID
        in: formData
        name: emp_id
        required: true
        type: string
      - description: Outlet ID
        in: formData
        name: outlet_id
        required: true
        type: string
      - description: Employee Name
        in: formData
        name: emp_name
        required: true
        type: string
      - description: Employee Email
        in: formData
        name: email
        required: true
        type: string
      - description: Additional document attachments
        in: formData
        name: addDocument[]
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Save/Update cuti details (Web)
      tags:
      - cuti
  /v1/schedules:
    get:
      consumes:
      - application/json
      description: Retrieve employee schedules by outlet, month and year
      parameters:
      - description: Query params
        in: body
        name: input
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Schedule data with status
          schema:
            properties:
              data:
                items:
                  additionalProperties: true
                  type: object
                type: array
              status:
                type: integer
            type: object
        "500":
          description: Internal server error
          schema: {}
      summary: Get employee schedules
      tags:
      - schedule
  /v1/shifts:
    get:
      consumes:
      - application/json
      description: Retrieve all shifts for a business
      produces:
      - application/json
      responses:
        "200":
          description: List of shifts
          schema:
            items:
              $ref: '#/definitions/domain.MasterShift'
            type: array
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
              status:
                type: integer
            type: object
      security:
      - BearerAuth: []
      summary: Get all shifts
      tags:
      - shift
  /v1/shifts/add:
    post:
      consumes:
      - application/json
      description: Create a new shift or update existing one
      parameters:
      - description: Shift details
        in: body
        name: input
        required: true
        schema:
          properties:
            shift_active:
              type: string
            shift_code:
              type: string
            shift_color:
              type: string
            shift_fkid:
              type: string
            shift_id:
              type: integer
            shift_in:
              type: string
            shift_office:
              items:
                type: string
              type: array
            shift_out:
              type: string
            shift_tolerance:
              type: string
            shift_type:
              type: integer
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            properties:
              message:
                type: string
              status:
                type: integer
            type: object
        "400":
          description: Validation error
          schema:
            properties:
              message:
                additionalProperties:
                  type: string
                type: object
              status:
                type: integer
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
              status:
                type: integer
            type: object
      security:
      - BearerAuth: []
      summary: Add or update shift
      tags:
      - shift
  /v1/shifts/delete:
    post:
      consumes:
      - application/json
      description: Delete an existing shift
      parameters:
      - description: Shift to delete
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/domain.ShiftAdd'
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            properties:
              error: {}
              message:
                type: string
              status:
                type: integer
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
              status:
                type: integer
            type: object
      security:
      - BearerAuth: []
      summary: Delete shift
      tags:
      - shift
  /v1/shifts/export:
    get:
      consumes:
      - application/json
      description: Export all shifts for a business to Excel file
      produces:
      - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      responses:
        "200":
          description: Excel file with shift data
          schema:
            type: file
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
              status:
                type: integer
            type: object
      security:
      - BearerAuth: []
      summary: Export shifts to Excel
      tags:
      - shift
  /v1/shifts/group:
    post:
      consumes:
      - application/json
      description: Retrieve shift groups by outlet IDs
      parameters:
      - description: Outlet IDs
        in: body
        name: input
        required: true
        schema:
          properties:
            outlet_id:
              items: {}
              type: array
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: List of shift groups
          schema:
            items:
              $ref: '#/definitions/domain.ShiftGroup'
            type: array
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get shift groups
      tags:
      - shift
  /v1/shifts/import:
    post:
      consumes:
      - multipart/form-data
      description: Import shifts from Excel file with outlet IDs
      parameters:
      - description: Comma-separated outlet IDs
        in: formData
        name: outlet_ids
        required: true
        type: string
      - description: Excel file with shift data
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            properties:
              message:
                type: string
              status:
                type: integer
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              message:
                type: string
              status:
                type: integer
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
              status:
                type: integer
            type: object
      security:
      - BearerAuth: []
      summary: Import shifts from Excel file
      tags:
      - shift
  /v1/shifts/template:
    get:
      consumes:
      - application/json
      description: Download an Excel template file with the correct format for importing
        shifts
      produces:
      - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      responses:
        "200":
          description: Excel template file
          schema:
            type: file
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
              status:
                type: integer
            type: object
      security:
      - BearerAuth: []
      summary: Download Excel template for shift import
      tags:
      - shift
  /v1/shifts/update_hot:
    post:
      consumes:
      - application/json
      description: Update multiple shifts at once
      parameters:
      - description: Collection of shifts to update
        in: body
        name: input
        required: true
        schema:
          properties:
            collection:
              items:
                additionalProperties: true
                type: object
              type: array
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            properties:
              message:
                type: string
              status:
                type: integer
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error: {}
              message:
                type: string
              status:
                type: integer
            type: object
      security:
      - BearerAuth: []
      summary: Bulk update shifts
      tags:
      - shift
  /v1/type/{id}:
    get:
      consumes:
      - application/json
      description: Get a specific employee type by its ID
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: Type ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Employee type details
          schema:
            $ref: '#/definitions/domain.HrmMasterType'
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get employee type by ID
      tags:
      - type
  /v1/type/add:
    post:
      consumes:
      - application/json
      description: Create a new employee type with validation
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: User ID
        in: header
        name: user_id
        required: true
        type: string
      - description: Employee type information
        in: body
        name: type
        required: true
        schema:
          $ref: '#/definitions/domain.HrmMasterType'
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "400":
          description: Validation error
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Add a new employee type
      tags:
      - type
  /v1/type/delete:
    post:
      consumes:
      - application/json
      description: Delete an existing employee type by ID
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: Employee type to delete (only type_id is required)
        in: body
        name: type
        required: true
        schema:
          $ref: '#/definitions/domain.HrmMasterType'
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Delete an employee type
      tags:
      - type
  /v1/type/update:
    post:
      consumes:
      - application/json
      description: Update an employee type with validation
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: User ID
        in: header
        name: user_id
        required: true
        type: string
      - description: Updated employee type information
        in: body
        name: type
        required: true
        schema:
          $ref: '#/definitions/domain.HrmMasterType'
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "400":
          description: Validation error
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Update an existing employee type
      tags:
      - type
  /v1/types:
    get:
      consumes:
      - application/json
      description: Get all employee types without filtering
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of all employee types
          schema:
            items:
              $ref: '#/definitions/domain.HrmMasterType'
            type: array
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get all employee types
      tags:
      - type
  /v2/add_cuti:
    post:
      consumes:
      - multipart/form-data
      description: Add a new cuti record with details and optional document attachments
        from mobile app
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: Employee ID
        in: formData
        name: hrm_employee_id
        required: true
        type: string
      - description: Outlet ID(s) (comma separated)
        in: formData
        name: outlet_fkid
        required: true
        type: string
      - description: Creation timestamp
        in: formData
        name: trc_created
        required: true
        type: string
      - description: Start date of cuti (YYYY-MM-DD)
        in: formData
        name: trc_date_start
        required: true
        type: string
      - description: End date of cuti (YYYY-MM-DD)
        in: formData
        name: trc_date_end
        required: true
        type: string
      - description: Reason for cuti
        in: formData
        name: trc_reason
        required: true
        type: string
      - description: Status of cuti
        in: formData
        name: trc_status
        required: true
        type: string
      - description: Type of cuti
        in: formData
        name: type
        required: true
        type: string
      - description: Document attachments
        in: formData
        name: document
        type: file
      - description: Transaction Cuti ID (for updates)
        in: formData
        name: trc_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with message and status
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Add a new cuti record (Mobile)
      tags:
      - cuti
  /v2/add_device:
    post:
      consumes:
      - multipart/form-data
      description: Register a new device with its token for push notifications
      parameters:
      - description: User type
        in: formData
        name: user_type
        required: true
        type: string
      - description: User ID
        in: formData
        name: user_id
        required: true
        type: string
      - description: Device type (android/ios)
        in: formData
        name: device
        required: true
        type: string
      - description: Device information
        in: formData
        name: device_info
        required: true
        type: string
      - description: FCM token
        in: formData
        name: token
        required: true
        type: string
      - description: Creation timestamp
        in: formData
        name: data_created
        required: true
        type: string
      - description: Update timestamp
        in: formData
        name: data_updated
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Register a device for notifications
      tags:
      - messaging
  /v2/add_message:
    post:
      consumes:
      - multipart/form-data
      description: Create a new notification message for a user
      parameters:
      - description: Message title
        in: formData
        name: title
        required: true
        type: string
      - description: Message content
        in: formData
        name: message
        required: true
        type: string
      - description: Read status (0/1)
        in: formData
        name: is_read
        required: true
        type: string
      - description: Viewed status (0/1)
        in: formData
        name: is_viewed
        required: true
        type: string
      - description: Creation timestamp
        in: formData
        name: data_created
        required: true
        type: string
      - description: Message type
        in: formData
        name: type
        required: true
        type: string
      - description: Receiver ID
        in: formData
        name: receiver_id
        required: true
        type: string
      - description: Admin ID
        in: formData
        name: admin_fkid
        required: true
        type: string
      - description: Notification type
        in: formData
        name: notification_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Add a new notification message
      tags:
      - messaging
  /v2/attendance/{outlet_id}/{date}/{emp_id}:
    get:
      consumes:
      - application/json
      description: Get attendance information for a specific employee at a given outlet
        on a specific date
      parameters:
      - description: Outlet ID
        in: path
        name: outlet_id
        required: true
        type: integer
      - description: Date (YYYY-MM-DD)
        in: path
        name: date
        required: true
        type: string
      - description: Employee ID
        in: path
        name: emp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Attendance information
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get employee attendance by outlet, date and employee ID (Mobile)
      tags:
      - cuti
  /v2/change_password:
    post:
      consumes:
      - multipart/form-data
      description: Change an employee's password with verification of old password
      parameters:
      - description: Employee email
        in: formData
        name: email
        required: true
        type: string
      - description: Old password
        in: formData
        name: old_pass
        required: true
        type: string
      - description: New password
        in: formData
        name: new_pass
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "400":
          description: Bad request or validation error
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Change employee password
      tags:
      - employee
  /v2/cuti_time_details/{emp_id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about cuti records for a specific employee.
      parameters:
      - description: Employee ID
        in: path
        name: emp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Employee cuti details
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get employee cuti details by employee ID (Mobile)
      tags:
      - cuti
  /v2/delete_messages:
    post:
      consumes:
      - multipart/form-data
      description: Delete multiple messages by their IDs
      parameters:
      - description: Comma-separated list of message IDs
        in: formData
        name: messages_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Delete multiple messages
      tags:
      - messaging
  /v2/delete_notif:
    post:
      consumes:
      - multipart/form-data
      description: Delete a specific notification by ID
      parameters:
      - description: Notification ID
        in: formData
        name: notif_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Delete a notification
      tags:
      - messaging
  /v2/emp_role/{emp_id}:
    get:
      consumes:
      - application/json
      description: Get the role information for a specific employee
      parameters:
      - description: Employee ID
        in: path
        name: emp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Employee role information
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get employee role by employee ID (Mobile)
      tags:
      - cuti
  /v2/employee:
    get:
      consumes:
      - application/json
      description: Get detailed employee data including job position, employee type
        and flattened additional information. Supports filtering by outlet IDs and
        employee type IDs.
      parameters:
      - description: Business ID
        in: header
        name: business_id
        required: true
        type: string
      - description: Comma-separated outlet IDs for filtering (e.g., 45,67,4)
        in: query
        name: outlet_ids
        type: string
      - description: Comma-separated employee type IDs for filtering (e.g., 1,2,3)
        in: query
        name: employee_type_ids
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of employees with flattened additional info
          schema:
            items:
              type: object
            type: array
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get detailed employee data with flattened additional info
      tags:
      - employee
  /v2/fetch_cuti_details/{trc_id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific cuti transaction
      parameters:
      - description: Transaction Cuti ID
        in: path
        name: trc_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Cuti details
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get cuti details by transaction ID (Mobile)
      tags:
      - cuti
  /v2/fetch_device/{token}:
    get:
      consumes:
      - application/json
      description: Get device information by FCM token
      parameters:
      - description: FCM token
        in: path
        name: token
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Device information
          schema:
            $ref: '#/definitions/domain.Device'
        "400":
          description: Bad request error
          schema:
            type: object
      summary: Get user device by token
      tags:
      - messaging
  /v2/fetch_employee_details/{emp_id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific employee
      parameters:
      - description: Employee ID
        in: path
        name: emp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Employee details
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get employee details by employee ID (Mobile)
      tags:
      - cuti
  /v2/fetch_employee_type/{emp_id}:
    get:
      consumes:
      - application/json
      description: Get the type information for a specific employee
      parameters:
      - description: Employee ID
        in: path
        name: emp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Employee type information
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get employee type by employee ID (Mobile)
      tags:
      - cuti
  /v2/fetch_messages/{type}/{emp_id}:
    get:
      consumes:
      - application/json
      description: Get all messages for a specific user filtered by type
      parameters:
      - description: Message type
        in: path
        name: type
        required: true
        type: string
      - description: Employee ID
        in: path
        name: emp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of messages
          schema:
            items:
              $ref: '#/definitions/domain.Message'
            type: array
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get user messages
      tags:
      - messaging
  /v2/fetch_outlet_latlng/{outlet_id}:
    get:
      consumes:
      - application/json
      description: Get latitude and longitude information for one or more outlets
      parameters:
      - description: Outlet ID(s) (comma separated)
        in: path
        name: outlet_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Outlet location information
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get outlet latitude and longitude (Mobile)
      tags:
      - cuti
  /v2/fetch_single_cuti_details/{trc_id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific cuti record for an employee
      parameters:
      - description: Transaction Cuti ID
        in: path
        name: trc_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Employee cuti information
          schema:
            type: object
        "400":
          description: Bad request error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get single employee cuti by transaction ID (Mobile)
      tags:
      - cuti
  /v2/get_vector/{emp_id}:
    get:
      consumes:
      - application/json
      description: Get the vector representation of an employee's profile image
      parameters:
      - description: Employee ID
        in: path
        name: emp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Image vector data
          schema:
            items:
              $ref: '#/definitions/domain.VectorImg'
            type: array
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get employee image vector
      tags:
      - employee
  /v2/scan_attendance:
    post:
      consumes:
      - multipart/form-data
      description: Record attendance for an employee with details like outlet, time,
        and code
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: Outlet ID
        in: formData
        name: tip_outlet_id
        required: true
        type: string
      - description: Employee name
        in: formData
        name: tip_nama_karyawan
        required: true
        type: string
      - description: Date (YYYY-MM-DD)
        in: formData
        name: tip_tanggal
        required: true
        type: string
      - description: Time (HH:MM:SS)
        in: formData
        name: tip_jam
        required: true
        type: string
      - description: Attendance code
        in: formData
        name: tip_kode
        required: true
        type: string
      - description: Employee ID
        in: formData
        name: employee_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with message and status
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Record employee attendance (Mobile)
      tags:
      - cuti
  /v2/shift_time/{outlet_id}/{emp_id}:
    get:
      consumes:
      - application/json
      description: Get the shift time for a specific employee at a given outlet for
        the current date.
      parameters:
      - description: Outlet ID
        in: path
        name: outlet_id
        required: true
        type: integer
      - description: Employee ID
        in: path
        name: emp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Shift time data
          schema:
            items:
              $ref: '#/definitions/domain.ShiftTime'
            type: array
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get employee shift time by outlet and employee ID (Mobile)
      tags:
      - cuti
  /v2/update_message_read:
    post:
      consumes:
      - multipart/form-data
      description: Update the read status of multiple messages
      parameters:
      - description: Comma-separated list of notification IDs
        in: formData
        name: where_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Mark messages as read
      tags:
      - messaging
  /v2/update_message_view:
    post:
      consumes:
      - multipart/form-data
      description: Update the viewed status of multiple messages
      parameters:
      - description: Comma-separated list of notification IDs
        in: formData
        name: where_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Mark messages as viewed
      tags:
      - messaging
  /v2/update_profile_photo:
    post:
      consumes:
      - multipart/form-data
      description: Update an employee's profile photo and generate vector representation
      parameters:
      - description: Employee ID
        in: formData
        name: emp_id
        required: true
        type: string
      - description: Profile photo
        in: formData
        name: photo
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Update employee profile photo
      tags:
      - employee
  /v2/update_user_device:
    post:
      consumes:
      - multipart/form-data
      description: Update device information for a specific user
      parameters:
      - description: User ID
        in: formData
        name: user_id
        required: true
        type: string
      - description: Device type (android/ios)
        in: formData
        name: device
        required: true
        type: string
      - description: Device record ID
        in: formData
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success response with status
          schema:
            type: object
        "400":
          description: Bad request error
          schema:
            type: object
      summary: Update user device information
      tags:
      - messaging
  /v2/user_devices/{emp_id}:
    get:
      consumes:
      - application/json
      description: Get all registered devices for a specific user
      parameters:
      - description: Employee ID
        in: path
        name: emp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of user devices
          schema:
            items:
              $ref: '#/definitions/domain.Device'
            type: array
        "500":
          description: Internal server error
          schema:
            type: object
      summary: Get user devices
      tags:
      - messaging
securityDefinitions:
  BearerAuth:
    description: Enter the token with the 'Bearer ' prefix, e.g. 'Bearer abcde12345'
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
