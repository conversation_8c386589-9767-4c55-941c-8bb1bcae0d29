package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	log "gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"golang.org/x/crypto/bcrypt"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLEmployeeRepository struct {
	mysql.Repository
}

// NewMySQLEmployeeRepository func
func NewMySQLEmployeeRepository(conn *sql.DB) domain.EmployeeRepository {
	return &mySQLEmployeeRepository{mysql.Repository{Conn: conn}}
}

func (m *mySQLEmployeeRepository) Fetch(adminFkid int) ([]domain.Employee, error) {
	query := `SELECT hem.hrm_employee_id AS HEMP_ID, hem.nik AS NIK, hem.hrm_employee_id AS ID,
	 hem.employee_sallary AS SALLARY, hem.employee_fkid AS HEMP_FKID, hem.type_fkid AS TYPE_FKID, 
	 hem.max_leave AS MAX_LEAVE, emp.name AS NAME, emp.employee_id AS EMP_ID, typ.type_name AS TYPE_NAME,
	 emp.date_join
	 FROM hrm_employee AS hem RIGHT 
	 JOIN employee AS emp ON emp.employee_id = hem.employee_fkid 
	 LEFT JOIN hrm_employee_type AS typ ON typ.type_id = hem.type_fkid 
	 WHERE emp.admin_fkid = ? 
	 AND emp.data_status = 'on' ORDER BY hrm_employee_id DESC`
	results, err := m.QueryArrayOld(query, adminFkid)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	resultJSON, err := json.Marshal(results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var result []domain.Employee
	var newResult []domain.Employee
	err = json.Unmarshal(resultJSON, &result)
	for k, v := range result {
		if v.NO == 0 {
			v.NO = k + 1
			newResult = append(newResult, v)
		}
	}
	return newResult, err
}

func (m *mySQLEmployeeRepository) FetchWithFilter(adminFkid int, filter domain.EmployeeFilter) ([]domain.Employee, error) {
	// Base query
	query := `SELECT DISTINCT hem.hrm_employee_id AS HEMP_ID, hem.nik AS NIK, hem.hrm_employee_id AS ID,
		 hem.employee_sallary AS SALLARY, hem.employee_fkid AS HEMP_FKID, hem.type_fkid AS TYPE_FKID,
		 hem.max_leave AS MAX_LEAVE, emp.name AS NAME, emp.employee_id AS EMP_ID, typ.type_name AS TYPE_NAME,
		 emp.date_join
		 FROM hrm_employee AS hem RIGHT
		 JOIN employee AS emp ON emp.employee_id = hem.employee_fkid
		 LEFT JOIN hrm_employee_type AS typ ON typ.type_id = hem.type_fkid`

	// Add outlet filter if needed
	if len(filter.OutletIDs) > 0 {
		query += ` JOIN employee_outlet AS eo ON emp.employee_id = eo.employee_fkid`
	}

	query += ` WHERE emp.admin_fkid = ? AND emp.data_status = 'on'`

	// Prepare arguments
	args := []interface{}{adminFkid}

	// Add outlet filter
	if len(filter.OutletIDs) > 0 {
		placeholders := strings.Repeat("?,", len(filter.OutletIDs))
		if len(placeholders) > 0 {
			placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
		}
		query += ` AND eo.outlet_fkid IN (` + placeholders + `)`
		for _, outletID := range filter.OutletIDs {
			args = append(args, outletID)
		}
	}

	// Add employee type filter
	if len(filter.EmployeeTypeIDs) > 0 {
		placeholders := strings.Repeat("?,", len(filter.EmployeeTypeIDs))
		if len(placeholders) > 0 {
			placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
		}
		query += ` AND hem.type_fkid IN (` + placeholders + `)`
		for _, typeID := range filter.EmployeeTypeIDs {
			args = append(args, typeID)
		}
	}

	query += ` ORDER BY hrm_employee_id DESC`

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	resultJSON, err := json.Marshal(results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var result []domain.Employee
	var newResult []domain.Employee
	err = json.Unmarshal(resultJSON, &result)
	for k, v := range result {
		if v.NO == 0 {
			v.NO = k + 1
			newResult = append(newResult, v)
		}
	}
	return newResult, err
}

func (m *mySQLEmployeeRepository) FetchSingle(hrmID int) (domain.EmployeeDetail, error) {
	var result domain.EmployeeDetail

	// Join query to get data from both hrm_employee and employee tables
	query := `
		SELECT
	he.hrm_employee_id,
	he.admin_fkid,
	he.employee_fkid,
	he.profile_img,
	he.data_status,
	he.nik,
	he.type_fkid,
	he.max_leave,
	he.employee_sallary,
	he.data_created AS hrm_data_created,
	he.data_modified AS hrm_data_modified,
	he.npwp,
	e.employee_id,
	e.name,
	e.address,
	e.phone,
	e.photo,
	e.last_login,
	e.jabatan_fkid,
	e.level,
	e.email,
	e.date_join,
	e.data_created AS emp_data_created,
	e.data_modified AS emp_data_modified,
	employees_jabatan.name AS employees_jabatan_name,
	hrm_employee_type.type_name AS hrm_employee_type_name
FROM
	hrm_employee he
	JOIN employee e ON he.employee_fkid = e.employee_id
	JOIN employees_jabatan ON e.jabatan_fkid = employees_jabatan.jabatan_id
	JOIN hrm_employee_type ON he.type_fkid = hrm_employee_type.type_id
WHERE
	he.hrm_employee_id = ?
	`

	err := m.Query(query, hrmID).Model(&result)
	if err != nil {
		log.IfError(err)
		return domain.EmployeeDetail{}, err
	}

	return result, nil
}

func (m *mySQLEmployeeRepository) FetchAddInfo(hrmIDs ...int) ([]domain.HrmAddInfo, error) {
	if len(hrmIDs) == 0 {
		return []domain.HrmAddInfo{}, nil
	}

	// Build placeholders for IN clause
	placeholders := strings.Repeat("?,", len(hrmIDs))
	if len(placeholders) > 0 {
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
	}

	// Build query
	query := fmt.Sprintf("SELECT * FROM hrm_additional_employee_info WHERE hrm_employee_fkid IN (%s)", placeholders)

	// Convert hrmIDs to []interface{} for QueryArrayOld
	args := make([]interface{}, len(hrmIDs))
	for i, id := range hrmIDs {
		args[i] = id
	}

	// Execute query
	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	// Convert to JSON and then to struct
	resultJSON, err := json.Marshal(results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var addInfo []domain.HrmAddInfo
	err = json.Unmarshal(resultJSON, &addInfo)
	return addInfo, err
}

func (m *mySQLEmployeeRepository) AddEmployee(employee domain.EmployeeData) (int64, error) {
	var hrmEmployeeID int64
	err := m.WithTransaction(func(tx mysql.Transaction) error {
		// Prepare data for employee table using ToEmployeeMap method
		// Use 25200 seconds (7 hours) as the time difference for Asia/Jakarta timezone
		employeeTableMap := employee.ToEmployeeMap(25200)

		// Prepare data for hrm_employee table
		hrmEmployeeTableMap := map[string]any{
			"nik":              employee.Nik,
			"type_fkid":        employee.TypeFkid,
			"max_leave":        employee.MaxLeave,
			"employee_sallary": employee.EmployeeSallary,
			"npwp":             employee.Npwp,
			"data_modified":    time.Now().UnixMilli(),
		}

		var employeeID int64 = cast.ToInt64(employee.EmployeeFkid)

		// Check if we're updating or inserting
		if employee.HrmEmployeeID != "" {
			log.Info("Updating employee, hrm_employee_id: %v", employee.HrmEmployeeID)
			// Update existing employee
			hrmEmployeeIDInt, _ := strconv.Atoi(employee.HrmEmployeeID)

			// First, get the employee_fkid from hrm_employee
			hrmEmployee, err := m.Query("SELECT employee_fkid FROM hrm_employee WHERE hrm_employee_id = ? limit 1", hrmEmployeeIDInt).Map()
			if log.IfError(err) {
				return err
			}

			if len(hrmEmployee) == 0 {
				return fmt.Errorf("employee not found")
			}

			empFkid := cast.ToInt(hrmEmployee["employee_fkid"])
			// Update employee table
			tx.Update("employee", employeeTableMap, "employee_id =?", empFkid)

			// Update hrm_employee table
			tx.Update("hrm_employee", hrmEmployeeTableMap, "hrm_employee_id =?", hrmEmployeeIDInt)
			hrmEmployeeID = int64(hrmEmployeeIDInt)

			// Handle outlet IDs for existing employee
			if employee.OutletIds != "" {
				// First, delete existing outlet associations
				_, err := m.Deletes("employee_outlet", map[string]interface{}{
					"employee_fkid": empFkid,
				})
				if err != nil {
					log.Info("Error deleting existing outlet associations: %v", err)
					return err
				}

				// Then insert new outlet associations
				outletIDs := strings.Split(employee.OutletIds, ",")
				for _, outletID := range outletIDs {
					outletIDInt, err := strconv.Atoi(strings.TrimSpace(outletID))
					if err != nil {
						log.Info("Invalid outlet ID: %v, error: %v", outletID, err)
						continue // Skip invalid outlet IDs
					}

					tx.Insert("employee_outlet", map[string]interface{}{
						"employee_fkid": empFkid,
						"outlet_fkid":   outletIDInt,
						"data_modified": time.Now(),
					})
				}
			}
		} else {
			// Insert new employee
			if employeeID == 0 {
				//add default values
				employeeTableMap["level"] = 0
				employeeTableMap["role_mobile"] = "{}"
				employeeTableMap["access_mode"] = "off"
				employeeTableMap["admin_fkid"] = employee.AdminId
				employeeTableMap["data_created"] = time.Now().UnixNano()
				employeeTableMap["data_modified"] = time.Now().UnixNano()
				var err error
				_, employeeID, err = m.InsertGetLastID("employee", employeeTableMap)
				if err != nil {
					return err
				}
			}

			// Add employee_fkid to hrm_employee data
			hrmEmployeeTableMap["employee_fkid"] = employeeID
			hrmEmployeeTableMap["data_created"] = time.Now().UnixMilli()

			// Insert into hrm_employee
			_, hrmEmployeeID, _ = m.InsertGetLastID("hrm_employee", hrmEmployeeTableMap)

			// Handle outlet IDs for new employee
			if employee.OutletIds != "" {
				outletIDs := strings.Split(employee.OutletIds, ",")
				for _, outletID := range outletIDs {
					outletIDInt, err := strconv.Atoi(strings.TrimSpace(outletID))
					if err != nil {
						log.Info("Invalid outlet ID: %v, error: %v", outletID, err)
						continue // Skip invalid outlet IDs
					}

					tx.Insert("employee_outlet", map[string]interface{}{
						"employee_fkid": employeeID,
						"outlet_fkid":   outletIDInt,
						"data_modified": time.Now(),
					})
				}
			}
		}
		return nil
	})

	return hrmEmployeeID, err
}

func (m *mySQLEmployeeRepository) GetAddInfo(empID int) ([]domain.HrmAddInfo, error) {
	info, err := m.QueryArrayOld("SELECT * FROM hrm_additional_employee_info WHERE hrm_employee_fkid=?", empID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resultJSON, err := json.Marshal(info)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var result []domain.HrmAddInfo
	err = json.Unmarshal(resultJSON, &result)
	return result, err
}

func (m *mySQLEmployeeRepository) AddInfo(addInfo []map[string]interface{}) error {
	if len(addInfo) == 0 {
		log.Info("no data to insert")
		return nil
	}
	_, err := m.BulkInsert("hrm_additional_employee_info", addInfo)
	return err
}

func (m *mySQLEmployeeRepository) UpdateInfo(updateInfo []map[string]interface{}) error {
	if len(updateInfo) == 0 {
		log.Info("no data to update")
		return nil
	}
	err := m.BulkUpdate("hrm_additional_employee_info", "add_id", updateInfo)
	return err
}

func (m *mySQLEmployeeRepository) DeleteAttach(where map[string]interface{}) error {
	_, err := m.Deletes("hrm_additional_employee_info", where)
	return err
}

func (m *mySQLEmployeeRepository) FetchEmpAttach(addID int) ([]domain.EmployeeAttach, error) {
	query := "SELECT attachment FROM hrm_additional_employee_info WHERE add_id=?"
	res, err := m.QueryArrayOld(query, addID)
	if err != nil {
		fmt.Printf("query fetch employee atachment error: %v", err)
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		fmt.Printf("marshalling error: %v", err)
		return nil, err
	}
	var results []domain.EmployeeAttach
	err = json.Unmarshal(resJSON, &results)
	return results, err
}

func (m *mySQLEmployeeRepository) FetchVectorImg(empID int) ([]domain.VectorImg, error) {
	query := "SELECT img_vector FROM hrm_employee WHERE employee_fkid=?"
	res, err := m.QueryArrayOld(query, empID)
	if err != nil {
		fmt.Printf("query error: %v", err)
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		fmt.Printf("marshalling response error: %v", err)
		return nil, err
	}
	var results []domain.VectorImg
	err = json.Unmarshal(resJSON, &results)
	return results, err
}

func (m *mySQLEmployeeRepository) UpdatePofilePhoto(empID int, img string, vector interface{}) error {

	data := map[string]interface{}{
		"img_vector":  vector,
		"profile_img": img,
	}
	where := map[string]interface{}{
		"employee_fkid": empID,
	}
	_, err := m.Updates("hrm_employee", data, where)
	if err != nil {
		fmt.Printf("error updating vector: %v", err)
		return err
	}
	return nil
}

func (m *mySQLEmployeeRepository) FetchEmpImg(empID int) (domain.ProfileImage, error) {
	query := "SELECT * FROM hrm_employee WHERE employee_fkid=?"
	var result domain.ProfileImage
	err := m.Query(query, empID).Model(&result)
	if err != nil {
		fmt.Printf("query employee profile image error: %v", err)
		return domain.ProfileImage{}, err
	}
	return result, err
}

func (m *mySQLEmployeeRepository) GetImageVector(file1 multipart.File, file2 multipart.File, fileName string, empID string) error {
	return nil
}

func (m *mySQLEmployeeRepository) ChangePassword(email string, oldPass string, newPass string) error {
	data := map[string]interface{}{
		"password": newPass,
	}
	where := map[string]interface{}{
		"email": email,
	}
	// update password in emplyee table
	_, err := m.Updates("employee", data, where)
	if err != nil {
		log.IfError(err)
		// update password in accounts table
		_, err := m.Updates("accounts", data, where)
		if err != nil {
			log.IfError(err)
			return err
		}
		loc, _ := time.LoadLocation("Asia/Jakarta")
		subject := "Kata sandi telah di ganti"
		randString := cast.RandStringBytes(32)
		now := time.Now().In(loc).Format("2006-01-02 15:04:05")
		emailLink := url.QueryEscape(email)
		urls := os.Getenv("EMAIL_URL") + `/confirmation/resetpass/` + randString + `/` + emailLink
		// email contents
		content := `<html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>noreply</title></head><body><div><br>============ Forwarded Message ============<br>From : <a href="mailto:noreply.uniq.id" target="_blank"><EMAIL></a><br>To : <a href="mailto:` + email + `">` + email + `</a><br>Date: ` + now + `<br>Subject: ` + subject + `<br>============ Forwarded Message ============<br><br><blockquote style="border-left:1px solid rgb(204,204,204);padding-left:6px;margin-left:5px"><div>Seseorang atau Anda telah melakukan pergantian kata sandi / <i>password</i>. Bila Anda tidak melakukan hal tersebut, silahkan klik link di bawah ini untuk melakukan pergantian password.<br>Link dibawah ini berlaku selama 3 hari sejak email ini dikirim. <br><br><h3><a href="` + urls + `" target="_blank">Klik Disini untuk Mengatur Ulang Password</a></h3><br><br>Atas perhatian Anda, kami ucapkan terima kasih. <br>UNIQ.</div></blockquote></div></body></html>`

		// data post values
		data := url.Values{}
		data.Set("email", email)
		data.Set("subject", subject)
		data.Set("content", content)

		req, err := http.NewRequest("POST", os.Getenv("MESSENGER_URL"), strings.NewReader(data.Encode()))
		req.Header.Set("Authorization", os.Getenv("MESSENGER_TOKEN"))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		if err != nil {
			log.IfError(err)
			return err
		}
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			fmt.Printf("error send email change password: \n %v", err)
			log.IfError(err)
			return err
		}

		defer resp.Body.Close()

		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			log.IfError(err)
		}
		fmt.Println(string(body))
		strBcrypt, err := bcrypt.GenerateFromPassword([]byte(randString), 12)
		if err != nil {
			log.IfError(err)
		}

		err = m.InsertUserKey(email, string(strBcrypt))
		if err != nil {
			log.IfError(err)
			return err
		}
		return nil
	}
	loc, _ := time.LoadLocation("Asia/Jakarta")
	subject := "Kata sandi telah di ganti"
	randString := cast.RandStringBytes(32)
	now := time.Now().In(loc).Format("2006-01-02 15:04:05")
	emailLink := url.QueryEscape(email)
	urls := os.Getenv("EMAIL_URL") + `confirmation/resetpass/` + randString + `/` + emailLink
	content := `<html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>noreply</title></head><body><div><br>============ Forwarded Message ============<br>From : <a href="mailto:noreply.uniq.id" target="_blank"><EMAIL></a><br>To : <a href="mailto:` + email + `">` + email + `</a><br>Date: ` + now + `<br>Subject: ` + subject + `<br>============ Forwarded Message ============<br><br><blockquote style="border-left:1px solid rgb(204,204,204);padding-left:6px;margin-left:5px"><div>Seseorang atau Anda telah melakukan pergantian kata sandi / <i>password</i>. Bila Anda tidak melakukan hal tersebut, silahkan klik link di bawah ini untuk melakukan pergantian password.<br>Link dibawah ini berlaku selama 3 hari sejak email ini dikirim. <br><br><h3><a href="` + urls + `" target="_blank">Klik Disini untuk Mengatur Ulang Password</a></h3><br><br>Atas perhatian Anda, kami ucapkan terima kasih. <br>UNIQ.</div></blockquote></div></body></html>`

	datas := url.Values{}
	datas.Set("email", email)
	datas.Set("subject", subject)
	datas.Set("content", content)

	req, err := http.NewRequest("POST", os.Getenv("MESSENGER_URL"), strings.NewReader(datas.Encode()))
	req.Header.Set("Authorization", os.Getenv("MESSENGER_TOKEN"))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	if err != nil {
		log.IfError(err)
		return err
	}
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.IfError(err)
		return err
	}

	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.IfError(err)
	}
	fmt.Println(string(body))
	strBcrypt, err := bcrypt.GenerateFromPassword([]byte(randString), 12)
	if err != nil {
		log.IfError(err)
	}

	err = m.InsertUserKey(email, string(strBcrypt))
	if err != nil {
		log.IfError(err)
		return err
	}
	return err
}

func (m *mySQLEmployeeRepository) FetchEmailNPassword(email string) (domain.EmailNPassword, error) {
	var result domain.EmailNPassword
	query := "SELECT email, password FROM employee WHERE email=?"
	err := m.Query(query, email).Model(&result)
	if err != nil {
		log.IfError(err)
		return domain.EmailNPassword{}, nil
	}

	return result, err
}

func (m *mySQLEmployeeRepository) InsertUserKey(email string, BcryptStr string) error {
	data := map[string]interface{}{
		"key_type":     "forgot",
		"user_level":   "employee",
		"email":        email,
		"secret_key":   BcryptStr,
		"data_created": cast.MakeTimestamp(),
		"data_expired": cast.MakeTimestampAdd(),
	}

	res, err := m.Insert("users_key", data)
	if err != nil {
		log.IfError(err)
	}
	fmt.Printf("inserted users_key: %v \n", res)
	return err
}

func (m *mySQLEmployeeRepository) FetchEmployeeOutlet(empID int) ([]domain.EmployeeOutlet, error) {
	query := "SELECT employee_fkid, outlet_fkid, name as outlet_name FROM employee_outlet JOIN outlets ON employee_outlet.outlet_fkid=outlets.outlet_id WHERE employee_fkid=?"
	var results []domain.EmployeeOutlet
	err := m.Query(query, empID).Model(&results)
	return results, err
}
