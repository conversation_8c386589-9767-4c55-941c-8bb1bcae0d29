# Employee API Update Test

## Updated API Endpoint

The `/v1/employee/{id}` endpoint has been updated to support fetching employee data by either:

1. **hrm_employee_id** (path parameter) - existing functionality
2. **employee_id** (query parameter) - new functionality for cases where hrm_employee_id is 0

## Usage Examples

### 1. Fetch by hrm_employee_id (existing behavior)
```
GET /v1/employee/123
```
This will fetch the employee with `hrm_employee_id = 123`

### 2. Fetch by employee_id (new behavior)
```
GET /v1/employee/0?employee_id=456
```
This will fetch the employee with `employee_id = 456` when `hrm_employee_id` is 0 or not available

### 3. Error case - missing both IDs
```
GET /v1/employee/0
```
This will return a 400 Bad Request error with message about missing required parameters

## Implementation Changes

### 1. Domain Interface Updated
- `FetchSingle(hrmID int, employeeID int) (EmployeeDetail, error)`

### 2. Repository Layer
- Updated query logic to handle both `hrm_employee_id` and `employee_id`
- Priority: if `hrmID > 0`, use hrm_employee_id; otherwise use employee_id

### 3. Use Case Layer
- Pass both parameters to repository

### 4. Handler Layer
- Parse both path parameter (`id`) and query parameter (`employee_id`)
- Validate that at least one ID is provided
- Return appropriate error messages

## Database Query Logic

The repository now uses conditional WHERE clauses:
- If `hrmID > 0`: `WHERE he.hrm_employee_id = ?`
- Else if `employeeID > 0`: `WHERE e.employee_id = ?`
- Else: return error

This ensures backward compatibility while adding the new functionality.
