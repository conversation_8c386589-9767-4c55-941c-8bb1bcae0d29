# Employee API Update Documentation

## Updated API Endpoint: `/v1/employee/{id}`

The employee fetch endpoint has been enhanced to support flexible employee data retrieval by either:

1. **hrm_employee_id** (path parameter) - for employees with HRM records
2. **employee_id** (query parameter) - for employees without HRM records (hrm_employee_id = 0)

## API Usage Examples

### 1. Fetch by hrm_employee_id (existing behavior)
```http
GET /v1/employee/123
```
**Use case**: Employee has an HRM record
**Result**: Fetches employee with `hrm_employee_id = 123`

### 2. Fetch by employee_id (new behavior)
```http
GET /v1/employee/0?employee_id=456
```
**Use case**: Employee exists but doesn't have HRM record yet
**Result**: Fetches employee with `employee_id = 456`

### 3. Error case - missing both IDs
```http
GET /v1/employee/0
```
**Result**: Returns 400 Bad Request with error message about missing required parameters

## Response Format

Both methods return the same response structure:
```json
[
  {
    // Employee details from both employee and hrm_employee tables
    "hrm_employee_id": 123,
    "employee_id": 456,
    "name": "<PERSON>",
    "nik": "1234567890123456",
    // ... other employee fields
  },
  [
    // Additional employee info array
    {
      "add_id": 1,
      "info_type": "education",
      // ... additional info fields
    }
  ]
]
```

## Implementation Changes

### 1. Domain Interface Updated
- `FetchSingle(hrmID int, employeeID int) (EmployeeDetail, error)`

### 2. Repository Layer
- Updated query logic to handle both `hrm_employee_id` and `employee_id`
- Priority: if `hrmID > 0`, use hrm_employee_id; otherwise use employee_id

### 3. Use Case Layer
- Pass both parameters to repository

### 4. Handler Layer
- Parse both path parameter (`id`) and query parameter (`employee_id`)
- Validate that at least one ID is provided
- Return appropriate error messages

## Database Query Logic

### Query Structure
The repository uses LEFT JOINs to handle both scenarios:
```sql
FROM employee e
LEFT JOIN hrm_employee he ON he.employee_fkid = e.employee_id
LEFT JOIN employees_jabatan ON e.jabatan_fkid = employees_jabatan.jabatan_id
LEFT JOIN hrm_employee_type ON he.type_fkid = hrm_employee_type.type_id
```

### Conditional WHERE Clauses
- If `hrmID > 0`: `WHERE he.hrm_employee_id = ?`
- Else if `employeeID > 0`: `WHERE e.employee_id = ?`
- Else: return error for missing parameters

### Benefits of LEFT JOIN Approach
1. **Flexibility**: Can fetch employees with or without HRM records
2. **Data Completeness**: Returns all available employee data even if some related records are missing
3. **Backward Compatibility**: Existing functionality remains unchanged
4. **Future-Proof**: Supports scenarios where employees are created before HRM records

## Error Handling

### 400 Bad Request
- Returned when both `hrm_employee_id` and `employee_id` are 0 or missing
- Message: "either hrm_employee_id (path parameter) or employee_id (query parameter) must be provided"

### 500 Internal Server Error
- Database connection issues
- Query execution errors
- Data processing errors

## Implementation Notes

- **Priority Logic**: hrm_employee_id takes precedence over employee_id when both are provided
- **Validation**: At least one valid ID (> 0) must be provided
- **Additional Info**: Fetched using the hrm_employee_id from the result (if available)
- **Outlets**: Employee outlet associations are fetched using employee_fkid
